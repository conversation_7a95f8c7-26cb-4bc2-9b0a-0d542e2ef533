#!/usr/bin/env python3
"""
Comprehensive codebase testing and cleanup script for AIMetaHarvest.
Tests all essential files and identifies unused/problematic files.
"""

import os
import sys
import ast
import importlib.util
import traceback
from pathlib import Path

class CodebaseAnalyzer:
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.tested_files = []
        self.unused_files = []
        self.core_files = []
        
    def test_python_syntax(self, file_path):
        """Test if a Python file has valid syntax"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse the AST to check syntax
            ast.parse(content)
            return True, None
        except SyntaxError as e:
            return False, f"Syntax error: {e}"
        except Exception as e:
            return False, f"Error reading file: {e}"
    
    def test_import_ability(self, file_path):
        """Test if a Python module can be imported"""
        try:
            # Convert file path to module name
            rel_path = os.path.relpath(file_path)
            module_name = rel_path.replace(os.sep, '.').replace('.py', '')
            
            # Skip certain files that shouldn't be imported directly
            skip_imports = [
                'run.py', 'celery_app.py', 'create_admin_user.py',
                'install_advanced_nlp_windows.py'
            ]
            
            if any(skip in file_path for skip in skip_imports):
                return True, "Skipped import test (entry point)"
            
            spec = importlib.util.spec_from_file_location(module_name, file_path)
            if spec is None:
                return False, "Could not create module spec"
            
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            return True, None
        except Exception as e:
            return False, f"Import error: {e}"
    
    def analyze_core_application_files(self):
        """Analyze core application files"""
        print("🔍 Testing Core Application Files...")
        
        core_files = [
            'run.py',
            'celery_app.py',
            'app/__init__.py',
            'app/config.py',
            'app/forms.py',
            'app/models/__init__.py',
            'app/models/user.py',
            'app/models/dataset.py',
            'app/models/metadata.py',
            'app/routes/__init__.py',
            'app/routes/auth.py',
            'app/routes/main.py',
            'app/routes/datasets.py',
            'app/routes/reports.py',
            'app/services/__init__.py',
            'app/services/dataset_service.py',
            'app/services/background_processing_service.py',
            'app/services/nlp_service.py',
            'app/services/quality_assessment_service.py',
            'app/services/report_generator.py',
            'app/utils/__init__.py',
            'app/utils/file_utils.py',
            'app/utils/validation_utils.py'
        ]
        
        for file_path in core_files:
            if os.path.exists(file_path):
                self.test_file(file_path, is_core=True)
            else:
                self.warnings.append(f"Core file missing: {file_path}")
    
    def test_file(self, file_path, is_core=False):
        """Test a single file"""
        print(f"  Testing: {file_path}")
        
        if is_core:
            self.core_files.append(file_path)
        
        # Test syntax
        syntax_ok, syntax_error = self.test_python_syntax(file_path)
        if not syntax_ok:
            self.errors.append(f"{file_path}: {syntax_error}")
            return False
        
        # Test imports (only for non-entry point files)
        if not any(skip in file_path for skip in ['run.py', 'celery_app.py']):
            import_ok, import_error = self.test_import_ability(file_path)
            if not import_ok:
                self.warnings.append(f"{file_path}: {import_error}")
        
        self.tested_files.append(file_path)
        return True
    
    def identify_unused_files(self):
        """Identify potentially unused files"""
        print("\n🗑️ Identifying Unused Files...")
        
        # Files that are clearly test/demo files and can be removed
        test_demo_files = [
            'test_*.py',
            'fix_*.py',
            'update_*.py',
            'verify_*.py',
            'final_verification.py',
            'dynamic_code_demo.py',
            'simple_pdf_test.py',
            '*_dataset_*.py',  # Generated demo files
            'cleanup_queue.py',
            'monitor_celery.bat'
        ]
        
        # Documentation files that might be redundant
        doc_files = [
            'FIELD_EXTRACTION_SUCCESS.md',
            'FINAL_SUCCESS_SUMMARY.md',
            'IMPROVEMENTS_SUMMARY.md',
            'DYNAMIC_PYTHON_CODE_SUCCESS.md'
        ]
        
        # Generated files
        generated_files = [
            '*.pdf',
            '__pycache__',
            '*.pyc',
            '.pytest_cache'
        ]
        
        all_files = []
        for root, dirs, files in os.walk('.'):
            # Skip certain directories
            if any(skip in root for skip in ['venv', '.git', '__pycache__', 'node_modules']):
                continue
            
            for file in files:
                file_path = os.path.join(root, file).replace('.\\', '')
                all_files.append(file_path)
        
        # Check against patterns
        for file_path in all_files:
            file_name = os.path.basename(file_path)
            
            # Check test/demo files
            for pattern in test_demo_files:
                if pattern.replace('*', '') in file_name or file_name.startswith(pattern.replace('*', '')):
                    self.unused_files.append(file_path)
                    break
            
            # Check doc files
            if file_name in doc_files:
                self.unused_files.append(file_path)
            
            # Check generated files
            for pattern in generated_files:
                if pattern.replace('*', '') in file_name and file_path not in self.core_files:
                    self.unused_files.append(file_path)
                    break
    
    def test_configuration_files(self):
        """Test configuration files"""
        print("\n⚙️ Testing Configuration Files...")
        
        config_files = [
            'requirements.txt',
            'Dockerfile',
            'docker-compose.yml',
            '.env.example' if os.path.exists('.env.example') else None
        ]
        
        for file_path in config_files:
            if file_path and os.path.exists(file_path):
                print(f"  ✅ Found: {file_path}")
                if file_path == 'requirements.txt':
                    self.test_requirements_file(file_path)
            else:
                self.warnings.append(f"Configuration file missing: {file_path}")
    
    def test_requirements_file(self, file_path):
        """Test requirements.txt for common issues"""
        try:
            with open(file_path, 'r') as f:
                lines = f.readlines()
            
            # Check for common issues
            for i, line in enumerate(lines, 1):
                line = line.strip()
                if line and not line.startswith('#'):
                    if '==' not in line and '>=' not in line and '<=' not in line:
                        self.warnings.append(f"requirements.txt line {i}: No version specified for {line}")
        except Exception as e:
            self.errors.append(f"Error reading requirements.txt: {e}")
    
    def test_directory_structure(self):
        """Test if required directories exist"""
        print("\n📁 Testing Directory Structure...")
        
        required_dirs = [
            'app',
            'app/models',
            'app/routes', 
            'app/services',
            'app/templates',
            'app/static',
            'app/utils',
            'uploads'
        ]
        
        for dir_path in required_dirs:
            if os.path.exists(dir_path):
                print(f"  ✅ {dir_path}")
            else:
                self.errors.append(f"Required directory missing: {dir_path}")
    
    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "="*60)
        print("📊 CODEBASE ANALYSIS REPORT")
        print("="*60)
        
        print(f"\n✅ Files Tested: {len(self.tested_files)}")
        print(f"🔧 Core Files: {len(self.core_files)}")
        print(f"❌ Errors: {len(self.errors)}")
        print(f"⚠️ Warnings: {len(self.warnings)}")
        print(f"🗑️ Unused Files: {len(self.unused_files)}")
        
        if self.errors:
            print(f"\n❌ ERRORS ({len(self.errors)}):")
            for error in self.errors:
                print(f"  - {error}")
        
        if self.warnings:
            print(f"\n⚠️ WARNINGS ({len(self.warnings)}):")
            for warning in self.warnings[:10]:  # Show first 10
                print(f"  - {warning}")
            if len(self.warnings) > 10:
                print(f"  ... and {len(self.warnings) - 10} more warnings")
        
        if self.unused_files:
            print(f"\n🗑️ UNUSED FILES TO REMOVE ({len(self.unused_files)}):")
            for unused in self.unused_files[:15]:  # Show first 15
                print(f"  - {unused}")
            if len(self.unused_files) > 15:
                print(f"  ... and {len(self.unused_files) - 15} more files")
        
        # Overall status
        print(f"\n{'='*60}")
        if len(self.errors) == 0:
            print("🎉 CODEBASE STATUS: HEALTHY")
            print("✅ No critical errors found")
            if len(self.warnings) > 0:
                print(f"⚠️ {len(self.warnings)} warnings to review")
        else:
            print("❌ CODEBASE STATUS: NEEDS ATTENTION")
            print(f"🔧 {len(self.errors)} errors need fixing")
        
        return len(self.errors) == 0

def main():
    """Main testing function"""
    print("🚀 AIMetaHarvest Codebase Analysis")
    print("="*60)
    
    analyzer = CodebaseAnalyzer()
    
    # Run all tests
    analyzer.test_directory_structure()
    analyzer.analyze_core_application_files()
    analyzer.test_configuration_files()
    analyzer.identify_unused_files()
    
    # Generate report
    success = analyzer.generate_report()
    
    # Cleanup recommendations
    if analyzer.unused_files:
        print(f"\n🧹 CLEANUP RECOMMENDATIONS:")
        print("Run the following commands to clean up unused files:")
        print("```bash")
        for unused in analyzer.unused_files[:10]:
            if os.path.isfile(unused):
                print(f"rm '{unused}'")
            elif os.path.isdir(unused):
                print(f"rm -rf '{unused}'")
        print("```")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
